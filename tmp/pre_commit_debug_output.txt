===================================
 pre-commit Debug Information      
 Timestamp: Thu Jun  5 07:37:24 PM CST 2025                
===================================
--- Current Working Directory ---
/home/<USER>/Codes/Go/sekai-go

PATH=/usr/lib/git-core:/home/<USER>/.cargo/bin:/home/<USER>/.local/bin:/home/<USER>/.local/share/pnpm:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/usr/games:/usr/local/games:/snap/bin:/home/<USER>/SDKs/flutter/bin:/home/<USER>/SDKs/flutter/bin/cache/dart-sdk/bin:/home/<USER>/Android/Sdk/platform-tools:/home/<USER>/SDKs/go/bin:/home/<USER>/go/bin:/home/<USER>/.lmstudio/bin:/home/<USER>/SDKs/flutter/bin:/home/<USER>/SDKs/flutter/bin/cache/dart-sdk/bin:/home/<USER>/Android/Sdk/platform-tools:/home/<USER>/SDKs/go/bin:/home/<USER>/go/bin
--- Files being processed by pre-commit (staged files) ---
.gitignore
.pre-commit-config.yaml
README.md
main.go
UID          PID    PPID  C STIME TTY          TIME CMD
moment      4327       1  0 19:36 ?        00:00:00 /usr/lib/systemd/systemd --user
moment      4328    4327  0 19:36 ?        00:00:00 (sd-pam)
moment      4342    4327  0 19:36 ?        00:00:00 /usr/bin/pipewire
moment      4343    4327  0 19:36 ?        00:00:00 /usr/bin/pipewire -c filter-chain.conf
moment      4344    4327  0 19:36 ?        00:00:00 /usr/bin/wireplumber
moment      4346    4327  0 19:36 ?        00:00:00 /usr/bin/pipewire-pulse
moment      4347    4327  0 19:36 ?        00:00:00 /usr/bin/gnome-keyring-daemon --foreground --components=pkcs11,secrets --control-directory=/run/user/1000/keyring
moment      4352    4327  0 19:36 ?        00:00:00 /usr/bin/dbus-daemon --session --address=systemd: --nofork --nopidfile --systemd-activation --syslog-only
moment      4365    4301  0 19:36 ?        00:00:00 /usr/lib/x86_64-linux-gnu/cinnamon-session-binary --session cinnamon
moment      4447       1  0 19:36 ?        00:00:00 /usr/bin/fcitx5 -d
moment      4480    4365  0 19:36 ?        00:00:00 /usr/bin/csd-wacom
moment      4481    4365  0 19:36 ?        00:00:00 /usr/bin/csd-clipboard
moment      4482    4365  0 19:36 ?        00:00:00 /usr/bin/csd-power
moment      4483    4365  0 19:36 ?        00:00:00 /usr/libexec/at-spi-bus-launcher --launch-immediately
moment      4484    4365  0 19:36 ?        00:00:00 /usr/bin/csd-xsettings
moment      4492    4365  0 19:36 ?        00:00:00 /usr/bin/csd-color
moment      4502    4365  0 19:36 ?        00:00:00 /usr/bin/csd-print-notifications
moment      4503    4483  0 19:36 ?        00:00:00 /usr/bin/dbus-daemon --config-file=/usr/share/defaults/at-spi2/accessibility.conf --nofork --print-address 11 --address=unix:path=/run/user/1000/at-spi/bus_0
moment      4504    4365  0 19:36 ?        00:00:00 /usr/bin/csd-housekeeping
moment      4505    4365  0 19:36 ?        00:00:00 /usr/bin/csd-screensaver-proxy
moment      4506    4365  0 19:36 ?        00:00:00 /usr/bin/csd-a11y-settings
moment      4510    4365  0 19:36 ?        00:00:00 /usr/bin/csd-keyboard
moment      4517    4365  0 19:36 ?        00:00:00 /usr/bin/csd-settings-remap
moment      4520    4365  0 19:36 ?        00:00:00 /usr/bin/csd-automount
moment      4524    4365  0 19:36 ?        00:00:00 /usr/bin/csd-media-keys
moment      4527    4365  2 19:36 ?        00:00:01 /usr/bin/csd-background
moment      4542    4327  0 19:36 ?        00:00:00 /usr/libexec/dconf-service
moment      4580    4327  0 19:36 ?        00:00:00 /usr/libexec/gvfsd
moment      4600    4327  0 19:36 ?        00:00:00 /usr/libexec/gvfsd-fuse /run/user/1000/gvfs -f
moment      4617       1  0 19:36 ?        00:00:00 /usr/libexec/at-spi2-registryd --use-gnome-session
moment      4619       1  0 19:36 ?        00:00:00 /usr/libexec/csd-printer
moment      4623    4327  0 19:36 ?        00:00:00 /usr/libexec/xdg-desktop-portal
moment      4625    4327  0 19:36 ?        00:00:00 /usr/libexec/gvfs-udisks2-volume-monitor
moment      4652    4327  0 19:36 ?        00:00:00 /usr/libexec/xdg-document-portal
moment      4659    4365  0 19:36 ?        00:00:00 cinnamon-launcher
moment      4665    4327  0 19:36 ?        00:00:00 /usr/libexec/gvfs-mtp-volume-monitor
moment      4666    4327  0 19:36 ?        00:00:00 /usr/libexec/xdg-permission-store
moment      4693    4327  0 19:36 ?        00:00:00 /usr/libexec/xdg-desktop-portal-xapp
moment      4696    4327  0 19:36 ?        00:00:00 /usr/libexec/gvfs-gphoto2-volume-monitor
moment      4712    4327  0 19:36 ?        00:00:00 /usr/libexec/gvfs-goa-volume-monitor
moment      4717    4327  0 19:36 ?        00:00:00 /usr/libexec/goa-daemon
moment      4722    4659 10 19:36 ?        00:00:06 cinnamon --replace
moment      4736    4327  0 19:36 ?        00:00:00 /usr/libexec/goa-identity-service
moment      4742    4327  0 19:36 ?        00:00:00 /usr/libexec/gvfs-afc-volume-monitor
moment      4784    4327  0 19:36 ?        00:00:00 /usr/libexec/xdg-desktop-portal-gtk
moment      4875    4365  0 19:36 ?        00:00:00 /usr/lib/x86_64-linux-gnu/xapps/xapp-sn-watcher
moment      4913    4365  2 19:36 ?        00:00:01 /usr/bin/python3 /usr/bin/solaar --window=hide
moment      4917    4365  0 19:36 ?        00:00:00 /usr/bin/flameshot
moment      4918    4365  0 19:36 ?        00:00:00 /usr/lib/caribou/caribou
moment      4919    4365  0 19:36 ?        00:00:00 /usr/libexec/geoclue-2.0/demos/agent
moment      4930    4365  0 19:36 ?        00:00:00 /usr/bin/nemo-desktop
moment      4932    4365  0 19:36 ?        00:00:00 /usr/libexec/evolution-data-server/evolution-alarm-notify
moment      4938    4365  0 19:36 ?        00:00:00 cinnamon-killer-daemon
moment      4939    4365  0 19:36 ?        00:00:00 /usr/bin/python3 /usr/bin/blueman-applet
moment      4941    4365  7 19:36 ?        00:00:04 /usr/bin/clash-verge
moment      5017    4327  0 19:36 ?        00:00:00 /usr/libexec/evolution-source-registry
moment      5110    4327  0 19:36 ?        00:00:00 /usr/libexec/evolution-calendar-factory
moment      5113       1  0 19:36 ?        00:00:00 /usr/bin/python3 /usr/bin/blueman-tray
moment      5119    4327  0 19:36 ?        00:00:00 /usr/libexec/bluetooth/obexd
moment      5155    4327  0 19:36 ?        00:00:00 /usr/libexec/evolution-addressbook-factory
moment      5347    4941  0 19:36 ?        00:00:00 /usr/lib/x86_64-linux-gnu/webkit2gtk-4.1/WebKitNetworkProcess 1 24 26
moment      5364    4941 21 19:36 ?        00:00:13 /usr/lib/x86_64-linux-gnu/webkit2gtk-4.1/WebKitWebProcess 26 29 31
moment      5706    4580  0 19:36 ?        00:00:00 /usr/libexec/gvfsd-trash --spawner :1.37 /org/gtk/gvfs/exec_spaw/0
moment      5717    4327  0 19:36 ?        00:00:00 /usr/libexec/gvfsd-metadata
moment      5836    4365  0 19:36 ?        00:00:00 /usr/bin/copyq
moment      5890    5836  0 19:36 ?        00:00:00 /usr/bin/copyq --clipboard-access monitorClipboard
moment      5897    5836  0 19:36 ?        00:00:00 /usr/bin/copyq eval -- runMenuCommandFilters()
moment      6228    6227  0 19:36 ?        00:00:00 /usr/share/rustdesk/rustdesk --server
moment      6270    6233  0 19:36 ?        00:00:00 /usr/share/rustdesk/rustdesk --tray
moment      6420    4365  7 19:36 ?        00:00:04 /usr/bin/bytedance-lark-stable
moment      6426    6420  0 19:36 ?        00:00:00 cat
moment      6427    6420  0 19:36 ?        00:00:00 cat
moment      6466    6420  1 19:36 ?        00:00:00 /proc/self/exe --type=gpu-process --no-sandbox --enable-crash-reporter=, --change-stack-guard-on-fork=enable --gpu-preferences=YAAAAAAAAAAgAAAEAAAAAAAAAAAAAAAAAABgAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAIAAAAAAAAAAgAAAAAAAAAAUAAAAUAAAACAAAAAAAAAAIAAAAAAAAAAgAAAAAAAAA --browser-runtime --aha-multi-profile --shared-files --smc-metadata=7798783,17324317282580995700,16011710476120972479,20971520 --field-trial-handle=0,i,446358907134045738,9000577331562240855,262144 --disable-features=HttpsUpgrades --variations-seed-version --crashpad-client-sock=13
moment      6471    6420  0 19:36 ?        00:00:00 /proc/self/exe --type=utility --utility-sub-type=storage.mojom.StorageService --lang=zh-CN --service-sandbox-type=utility --no-sandbox --enable-crash-reporter=, --change-stack-guard-on-fork=enable --browser-runtime --aha-multi-profile --shared-files --smc-metadata=7798783,17324317282580995700,16011710476120972479,20971520 --field-trial-handle=0,i,446358907134045738,9000577331562240855,262144 --disable-features=HttpsUpgrades --variations-seed-version --crashpad-client-sock=15
moment      6481    6420  5 19:36 ?        00:00:03 /opt/bytedance/lark/lark --type=backend --task-jssdk-enabled --enable-backend-async-progress --disable-features=HttpsUpgrades --no-proxy-server --smc-metadata=7798783,17324317282580995700,16011710476120972479,20971520 --aha-multi-profile --enable-backend-async-progress --mojo-platform-channel-handle=3 --crashpad-client-sock=17
moment      6514    6420  4 19:36 ?        00:00:02 /proc/self/exe --type=utility --utility-sub-type=jash.mojom.JSRuntimeService --lang=zh-CN --service-sandbox-type=none --enable-backend-async-progress --no-sandbox --enable-crash-reporter=, --change-stack-guard-on-fork=enable --browser-runtime --aha-multi-profile --shared-files --smc-metadata=7798783,17324317282580995700,16011710476120972479,20971520 --field-trial-handle=0,i,446358907134045738,9000577331562240855,262144 --disable-features=HttpsUpgrades --variations-seed-version --crashpad-client-sock=19
moment      6647    6420  0 19:36 ?        00:00:00 /proc/self/exe --type=renderer --no-sandbox --enable-crash-reporter=, --change-stack-guard-on-fork=enable --first-renderer-process --no-zygote --indexeddb-idle-check-seconds=5 --scene=profile_main --enable-renderer-jank-monitor --enable-native-resource-code-cache --lang=zh-CN --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=8 --time-ticks-at-unix-epoch=-1749123328405974 --browser-runtime --aha-multi-profile --launch-time-ticks=63048117 --shared-files --smc-metadata=7798783,17324317282580995700,16011710476120972479,20971520 --field-trial-handle=0,i,446358907134045738,9000577331562240855,262144 --disable-features=HttpsUpgrades --variations-seed-version --crashpad-client-sock=21
moment      6648    6420  7 19:36 ?        00:00:03 /proc/self/exe --type=renderer --no-sandbox --enable-crash-reporter=, --change-stack-guard-on-fork=enable --no-zygote --indexeddb-idle-check-seconds=5 --module=renderer-messenger-standalone --scene=profile_main --enable-renderer-jank-monitor --enable-native-resource-code-cache --lang=zh-CN --num-raster-threads=4 --enable-main-frame-before-activation --renderer-client-id=7 --time-ticks-at-unix-epoch=-1749123328405974 --browser-runtime --aha-multi-profile --launch-time-ticks=63056825 --shared-files --smc-metadata=7798783,17324317282580995700,16011710476120972479,20971520 --field-trial-handle=0,i,446358907134045738,9000577331562240855,262144 --disable-features=HttpsUpgrades --variations-seed-version --crashpad-client-sock=23
moment      6678    6420  0 19:36 ?        00:00:00 /opt/bytedance/lark/lark --type=monitor --restart-count=1 --disable-features=HttpsUpgrades --no-proxy-server --smc-metadata=7798783,17324317282580995700,16011710476120972479,20971520 --mojo-platform-channel-handle=3 --crashpad-server-socks=12,26,28,30,32,35,37,39,41,43,45,47,49,51,53,55,57,59,61,63,65,67,69,71,73,75,77,79,81,83,85,87,89,91,93,95,97,99,101,103,105 --crashpad-client-sock=11
moment      6926       1  0 19:36 ?        00:00:00 mintUpdate
moment      7030    4722 99 19:36 ?        00:01:16 /home/<USER>/Softwares/GoLand/bin/goland
moment      7159    7030  0 19:36 ?        00:00:00 /home/<USER>/Softwares/GoLand/bin/fsnotifier
moment      7330    4327  0 19:36 ?        00:00:00 /usr/libexec/gnome-terminal-server
moment      7531    7030  0 19:36 ?        00:00:00 /home/<USER>/Softwares/GoLand/jbr/lib/cef_server --pipe=/tmp/cef_server_pipe_7030_07_36_49_687 --logfile=/home/<USER>/.cache/JetBrains/GoLand2025.1/log/jcef_7030.log --loglevel=100 --params=/tmp/cef_server_params.txt
moment      7540    7531  0 19:36 ?        00:00:00 /home/<USER>/Softwares/GoLand/jbr/lib/cef_server --type=zygote --no-zygote-sandbox --no-sandbox --force-device-scale-factor=2.0 --log-severity=disable --lang=en-US --user-data-dir=/home/<USER>/.cache/JetBrains/GoLand2025.1/jcef_cache --log-file=/home/<USER>/.cache/JetBrains/GoLand2025.1/log/jcef_7030.log
moment      7541    7531  0 19:36 ?        00:00:00 /home/<USER>/Softwares/GoLand/jbr/lib/cef_server --type=zygote --no-sandbox --force-device-scale-factor=2.0 --log-severity=disable --lang=en-US --user-data-dir=/home/<USER>/.cache/JetBrains/GoLand2025.1/jcef_cache --log-file=/home/<USER>/.cache/JetBrains/GoLand2025.1/log/jcef_7030.log
moment      7566    7540  0 19:36 ?        00:00:00 /home/<USER>/Softwares/GoLand/jbr/lib/cef_server --type=gpu-process --no-sandbox --log-severity=disable --lang=en-US --user-data-dir=/home/<USER>/.cache/JetBrains/GoLand2025.1/jcef_cache --gpu-preferences=WAAAAAAAAAAgAAAEAAAAAAAAAAAAAAAAAABgAAAAAAA4AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABAAAAGAAAAAAAAAAYAAAAAAAAAAgAAAAAAAAACAAAAAAAAAAIAAAAAAAAAA== --log-file=/home/<USER>/.cache/JetBrains/GoLand2025.1/log/jcef_7030.log --shared-files --field-trial-handle=0,i,8067107304924857072,13959224284492573091,262144 --disable-features=SpareRendererForSitePerProcess --variations-seed-version
moment      7630    7531  0 19:36 ?        00:00:00 /home/<USER>/Softwares/GoLand/jbr/lib/cef_server --type=utility --utility-sub-type=network.mojom.NetworkService --lang=zh-CN --service-sandbox-type=none --no-sandbox --proxy-server=http://127.0.0.1:7897 --log-severity=disable --lang=en-US --user-data-dir=/home/<USER>/.cache/JetBrains/GoLand2025.1/jcef_cache --log-file=/home/<USER>/.cache/JetBrains/GoLand2025.1/log/jcef_7030.log --shared-files=v8_context_snapshot_data:100 --field-trial-handle=0,i,8067107304924857072,13959224284492573091,262144 --disable-features=SpareRendererForSitePerProcess --variations-seed-version
moment      7638    7541  0 19:36 ?        00:00:00 /home/<USER>/Softwares/GoLand/jbr/lib/cef_server --type=utility --utility-sub-type=storage.mojom.StorageService --lang=zh-CN --service-sandbox-type=utility --no-sandbox --proxy-server=http://127.0.0.1:7897 --log-severity=disable --lang=en-US --user-data-dir=/home/<USER>/.cache/JetBrains/GoLand2025.1/jcef_cache --log-file=/home/<USER>/.cache/JetBrains/GoLand2025.1/log/jcef_7030.log --shared-files=v8_context_snapshot_data:100 --field-trial-handle=0,i,8067107304924857072,13959224284492573091,262144 --disable-features=SpareRendererForSitePerProcess --variations-seed-version
moment      7698    4365  0 19:36 ?        00:00:00 /usr/bin/python3 /usr/share/system-config-printer/applet.py
moment      8097       1  2 19:36 ?        00:00:00 mintreport-tray
moment      8550    7030  0 19:37 ?        00:00:00 /usr/bin/git -c credential.helper= -c core.quotepath=false -c log.showSignature=false commit -F /tmp/git-commit-msg-.txt --
moment      8551    8550 43 19:37 ?        00:00:00 /home/<USER>/.pyenv/versions/3.12.9/bin/python3.12 -mpre_commit hook-impl --config=.pre-commit-config.yaml --hook-type=pre-commit --hook-dir /home/<USER>/Codes/Go/sekai-go/.git/hooks --
moment      8575    8551  0 19:37 ?        00:00:00 /bin/bash ./scripts/debug_hook.sh
moment      8578    8575  0 19:37 ?        00:00:00 ps -f

===================================
 Debug Information End             
===================================
