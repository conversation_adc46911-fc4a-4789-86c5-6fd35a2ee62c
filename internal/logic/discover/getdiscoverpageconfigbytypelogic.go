package discover

import (
	"context"
	"encoding/json"

	"github.com/sekai-app/sekai-go/internal/db/pg"
	"github.com/sekai-app/sekai-go/internal/svc"
	"github.com/sekai-app/sekai-go/internal/types"
	"github.com/sekai-app/sekai-go/internal/values"

	"github.com/zeromicro/go-zero/core/logc"
	xerrors "github.com/zeromicro/x/errors"
)

type GetDiscoverPageConfigByTypeLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 根据类型获取发现页面配置
func NewGetDiscoverPageConfigByTypeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDiscoverPageConfigByTypeLogic {
	return &GetDiscoverPageConfigByTypeLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDiscoverPageConfigByTypeLogic) GetDiscoverPageConfigByType(req *types.GetDiscoverPageConfigByTypeReq) (resp *types.GetDiscoverPageConfigByTypeResp, err error) {
	logc.Infof(l.ctx, "GetDiscoverPageConfigByType request: %+v", req)

	dbParams := pg.GetDiscoverPageConfigByTypeParams{
		Type:     req.Type,
		Page:     int32(req.Page),
		PageSize: int32(req.Size),
	}

	// 首先获取总条目数
	totalCount, err := l.svcCtx.DB.CountDiscoverPageConfigByType(l.ctx, req.Type)
	if err != nil {
		logc.Errorf(l.ctx, "Failed to count discover page config by type from DB, error: %v, type: %s", err, req.Type)
		return nil, xerrors.New(values.ErrorCodeInternalDBError, "Failed to retrieve discover configurations count")
	}

	var apiItems []*types.DiscoverPageConfigItem
	var dbResult []*pg.GetDiscoverPageConfigByTypeRow

	if totalCount > 0 {
		dbResult, err = l.svcCtx.DB.GetDiscoverPageConfigByType(l.ctx, &dbParams)
		if err != nil {
			logc.Errorf(l.ctx, "Failed to get discover page config by type from DB, error: %v, params: %+v", err, dbParams)
			return nil, xerrors.New(values.ErrorCodeInternalDBError, "Failed to retrieve discover configurations")
		}
	} else {
		// 如果总数为0，则无需查询具体条目
		dbResult = make([]*pg.GetDiscoverPageConfigByTypeRow, 0)
	}
	
	apiItems = make([]*types.DiscoverPageConfigItem, 0, len(dbResult))
	for _, dbItem := range dbResult {
		var dataField types.DiscoverPageConfigData
		if errJ := json.Unmarshal(dbItem.Data, &dataField); errJ != nil {
			logc.Errorf(l.ctx, "Failed to unmarshal data field for item ID %d: %v. Raw Data: %s", dbItem.ID, errJ, string(dbItem.Data))
			return nil, xerrors.New(values.ErrorCodeInternalServerError, "Failed to process discover configuration data")
		}

		apiItem := &types.DiscoverPageConfigItem{
			ID:        dbItem.ID,
			Type:      dbItem.Type,
			Data:      dataField,
			Status:    dbItem.Status,
			SortOrder: dbItem.SortOrder,
			CreatedAt: 0,
			UpdatedAt: 0,
		}
		if dbItem.CreatedAt.Valid {
			apiItem.CreatedAt = dbItem.CreatedAt.Time.Unix()
		}
		if dbItem.UpdatedAt.Valid {
			apiItem.UpdatedAt = dbItem.UpdatedAt.Time.Unix()
		}
		apiItems = append(apiItems, apiItem)
	}
	
	resp = &types.GetDiscoverPageConfigByTypeResp{
		ResponseBase: types.ResponseBase{
			Code: values.ErrorCodeSuccess,
			Msg:  "success",
		},
		Data: &types.DiscoverPageConfigPagination{
			Pagination: types.Pagination{
				Page:    req.Page,
				Size:    req.Size,
				Total:   int(totalCount),
				Pages:   (int(totalCount) + req.Size - 1) / req.Size,
			},
			Items: apiItems,
		},
	}

	return resp, nil
}
